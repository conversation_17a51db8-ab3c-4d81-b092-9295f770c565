//
//  SMSReminderScheduler.swift
//  
//
//  Created by <PERSON> on 7/25/25.
//

import Foundation
import Vapor
import SotoCore
import SotoScheduler
import SotoEventBridge

// MARK: - SMS Reminder Data Models

struct SMSReminderPayload: Codable {
    let phoneNumber: String
    let message: String
    let appointmentId: String?
    let taskId: String?
    let goalId: String?
    let interventionId: String?
    let reminderType: ReminderType
    
    enum ReminderType: String, Codable {
        case appointment24h = "appointment_24h"
        case appointment48h = "appointment_48h"
        case taskDue = "task_due"
        case goalTarget = "goal_target"
        case interventionDue = "intervention_due"
        case followUp = "follow_up"
        case programTask = "program_task"
    }
}

struct ScheduleReminderInput {
    let entityId: UUID
    let entityType: SMSReminderPayload.ReminderType
    let phoneNumber: String
    let scheduledDate: Date
    let message: String
    let memberName: String?
}

// MARK: - SMS Reminder Scheduler Service

public class SMSReminderScheduler {
    private let client: AWSClient
    private let scheduler: Scheduler
    private let lambdaFunctionArn: String
    private let scheduleGroupName: String
    
    init(client: AWSClient) {
        self.client = client
        // Use configurable region, defaulting to us-east-2 to match Lambda deployment
        let regionString = Environment.get("SMS_REMINDER_AWS_REGION") ?? "us-east-2"
        let region = Region(rawValue: regionString)
        self.scheduler = Scheduler(client: client, region: region)
        self.lambdaFunctionArn = Environment.get("SMS_REMINDER_LAMBDA_ARN") ?? ""
        self.scheduleGroupName = Environment.get("SMS_REMINDER_SCHEDULE_GROUP") ?? "sms-reminders"
    }
    
    // MARK: - Public Methods
    
    /// Schedule SMS reminders for an appointment (24h and 48h before)
    func scheduleAppointmentReminders(
        req: Request,
        appointmentId: UUID,
        phoneNumber: String,
        appointmentDate: Date,
        memberName: String,
        appointmentTitle: String
    ) -> EventLoopFuture<Void> {
        let reminders = [
            (hours: 48, type: SMSReminderPayload.ReminderType.appointment48h),
            (hours: 24, type: SMSReminderPayload.ReminderType.appointment24h)
        ]
        
        let futures = reminders.compactMap { reminder -> EventLoopFuture<Void>? in
            let reminderDate = appointmentDate.addingTimeInterval(-TimeInterval(reminder.hours * 3600))
            
            // Don't schedule reminders in the past
            guard reminderDate > Date() else { return nil }
            
            let message = createAppointmentReminderMessage(
                memberName: memberName,
                appointmentTitle: appointmentTitle,
                appointmentDate: appointmentDate,
                hoursUntil: reminder.hours
            )
            
            let input = ScheduleReminderInput(
                entityId: appointmentId,
                entityType: reminder.type,
                phoneNumber: phoneNumber,
                scheduledDate: reminderDate,
                message: message,
                memberName: memberName
            )
            
            return scheduleReminder(req: req, input: input)
        }
        
        return req.eventLoop.flatten(futures)
    }
    
    /// Schedule SMS reminder for a task due date
    func scheduleTaskReminder(
        req: Request,
        taskId: UUID,
        phoneNumber: String,
        dueDate: Date,
        memberName: String,
        taskTitle: String
    ) -> EventLoopFuture<Void> {
        let reminderDate = dueDate.addingTimeInterval(-TimeInterval(24 * 3600)) // 24h before
        
        // Don't schedule reminders in the past
        guard reminderDate > Date() else {
            return req.eventLoop.makeSucceededFuture(())
        }
        
        let message = createTaskReminderMessage(
            memberName: memberName,
            taskTitle: taskTitle,
            dueDate: dueDate
        )
        
        let input = ScheduleReminderInput(
            entityId: taskId,
            entityType: .taskDue,
            phoneNumber: phoneNumber,
            scheduledDate: reminderDate,
            message: message,
            memberName: memberName
        )
        
        return scheduleReminder(req: req, input: input)
    }
    
    /// Schedule SMS reminder for a goal target date
    func scheduleGoalReminder(
        req: Request,
        goalId: UUID,
        phoneNumber: String,
        targetDate: Date,
        memberName: String,
        goalTitle: String
    ) -> EventLoopFuture<Void> {
        let reminderDate = targetDate.addingTimeInterval(-TimeInterval(24 * 3600)) // 24h before
        
        // Don't schedule reminders in the past
        guard reminderDate > Date() else {
            return req.eventLoop.makeSucceededFuture(())
        }
        
        let message = createGoalReminderMessage(
            memberName: memberName,
            goalTitle: goalTitle,
            targetDate: targetDate
        )
        
        let input = ScheduleReminderInput(
            entityId: goalId,
            entityType: .goalTarget,
            phoneNumber: phoneNumber,
            scheduledDate: reminderDate,
            message: message,
            memberName: memberName
        )
        
        return scheduleReminder(req: req, input: input)
    }
    
    /// Schedule SMS reminder for an intervention due date
    func scheduleInterventionReminder(
        req: Request,
        interventionId: UUID,
        phoneNumber: String,
        dueDate: Date,
        memberName: String,
        interventionAction: String
    ) -> EventLoopFuture<Void> {
        let reminderDate = dueDate.addingTimeInterval(-TimeInterval(24 * 3600)) // 24h before
        
        // Don't schedule reminders in the past
        guard reminderDate > Date() else {
            return req.eventLoop.makeSucceededFuture(())
        }
        
        let message = createInterventionReminderMessage(
            memberName: memberName,
            interventionAction: interventionAction,
            dueDate: dueDate
        )
        
        let input = ScheduleReminderInput(
            entityId: interventionId,
            entityType: .interventionDue,
            phoneNumber: phoneNumber,
            scheduledDate: reminderDate,
            message: message,
            memberName: memberName
        )
        
        return scheduleReminder(req: req, input: input)
    }

    /// Schedule SMS reminder for a follow-up appointment
    func scheduleFollowUpReminder(
        req: Request,
        followUpId: UUID,
        phoneNumber: String,
        followUpDate: Date,
        memberName: String,
        followUpType: String
    ) -> EventLoopFuture<Void> {
        let reminderDate = followUpDate.addingTimeInterval(-TimeInterval(24 * 3600)) // 24h before

        // Don't schedule reminders in the past
        guard reminderDate > Date() else {
            return req.eventLoop.makeSucceededFuture(())
        }

        let message = createFollowUpReminderMessage(
            memberName: memberName,
            followUpType: followUpType,
            followUpDate: followUpDate
        )

        let input = ScheduleReminderInput(
            entityId: followUpId,
            entityType: .followUp,
            phoneNumber: phoneNumber,
            scheduledDate: reminderDate,
            message: message,
            memberName: memberName
        )

        return scheduleReminder(req: req, input: input)
    }

    /// Schedule SMS reminder for a program task due date
    func scheduleProgramTaskReminder(
        req: Request,
        programTaskId: UUID,
        phoneNumber: String,
        dueDate: Date,
        memberName: String,
        taskTitle: String
    ) -> EventLoopFuture<Void> {
        let reminderDate = dueDate.addingTimeInterval(-TimeInterval(24 * 3600)) // 24h before

        // Don't schedule reminders in the past
        guard reminderDate > Date() else {
            return req.eventLoop.makeSucceededFuture(())
        }

        let message = createProgramTaskReminderMessage(
            memberName: memberName,
            taskTitle: taskTitle,
            dueDate: dueDate
        )

        let input = ScheduleReminderInput(
            entityId: programTaskId,
            entityType: .programTask,
            phoneNumber: phoneNumber,
            scheduledDate: reminderDate,
            message: message,
            memberName: memberName
        )

        return scheduleReminder(req: req, input: input)
    }

    /// Cancel all reminders for a specific entity
    func cancelReminders(req: Request, entityId: UUID, entityType: SMSReminderPayload.ReminderType) -> EventLoopFuture<Void> {
        let scheduleName = generateScheduleName(entityId: entityId, type: entityType)

        return scheduler.deleteSchedule(Scheduler.DeleteScheduleInput(
            groupName: scheduleGroupName,
            name: scheduleName
        )).map { _ in
            // Log successful cancellation
            req.smsReminderLogger.logReminderCancelled(
                entityType: entityType,
                entityId: entityId,
                reason: "Entity completed or cancelled"
            )
        }.flatMapError { error in
            // Check if it's a "not found" error, which is acceptable
            let errorString = String(describing: error)
            if errorString.contains("ResourceNotFoundException") || errorString.contains("not found") {
                req.logger.info("Schedule \(scheduleName) not found - may have already been executed or cancelled")
                req.smsReminderLogger.logReminderCancelled(
                    entityType: entityType,
                    entityId: entityId,
                    reason: "Schedule not found (already executed or cancelled)"
                )
                return req.eventLoop.makeSucceededFuture(())
            } else {
                // Log actual cancellation errors
                let wrappedError = SMSReminderError.awsSchedulerError(error)
                req.smsReminderLogger.logCancellationError(
                    entityType: entityType,
                    entityId: entityId,
                    error: wrappedError
                )
                // Don't fail the operation for cancellation errors
                return req.eventLoop.makeSucceededFuture(())
            }
        }
    }
    
    /// Cancel appointment reminders (both 24h and 48h)
    func cancelAppointmentReminders(req: Request, appointmentId: UUID) -> EventLoopFuture<Void> {
        let futures = [
            cancelReminders(req: req, entityId: appointmentId, entityType: .appointment24h),
            cancelReminders(req: req, entityId: appointmentId, entityType: .appointment48h)
        ]
        
        return req.eventLoop.flatten(futures)
    }
}

// MARK: - Private Helper Methods

extension SMSReminderScheduler {
    
    private func scheduleReminder(req: Request, input: ScheduleReminderInput) -> EventLoopFuture<Void> {
        // Validate input
        guard !input.phoneNumber.isEmpty else {
            let error = SMSReminderError.missingPhoneNumber
            req.smsReminderLogger.logSchedulingError(
                entityType: input.entityType,
                entityId: input.entityId,
                error: error,
                phoneNumber: input.phoneNumber
            )
            return req.eventLoop.makeFailedFuture(error)
        }

        guard isValidPhoneNumber(input.phoneNumber) else {
            let error = SMSReminderError.invalidPhoneNumber(input.phoneNumber)
            req.smsReminderLogger.logSchedulingError(
                entityType: input.entityType,
                entityId: input.entityId,
                error: error,
                phoneNumber: input.phoneNumber
            )
            return req.eventLoop.makeFailedFuture(error)
        }

        guard !lambdaFunctionArn.isEmpty else {
            let error = SMSReminderError.configurationError("Lambda function ARN not configured")
            req.smsReminderLogger.logSchedulingError(
                entityType: input.entityType,
                entityId: input.entityId,
                error: error
            )
            return req.eventLoop.makeFailedFuture(error)
        }

        let payload = SMSReminderPayload(
            phoneNumber: input.phoneNumber,
            message: input.message,
            appointmentId: input.entityType.isAppointmentType ? input.entityId.uuidString : nil,
            taskId: input.entityType == .taskDue ? input.entityId.uuidString : nil,
            goalId: input.entityType == .goalTarget ? input.entityId.uuidString : nil,
            interventionId: input.entityType == .interventionDue ? input.entityId.uuidString : nil,
            reminderType: input.entityType
        )

        guard let payloadData = try? JSONEncoder().encode(payload),
              let payloadString = String(data: payloadData, encoding: .utf8) else {
            let error = SMSReminderError.configurationError("Failed to encode reminder payload")
            req.smsReminderLogger.logSchedulingError(
                entityType: input.entityType,
                entityId: input.entityId,
                error: error
            )
            return req.eventLoop.makeFailedFuture(error)
        }

        let scheduleName = generateScheduleName(entityId: input.entityId, type: input.entityType)
        let scheduleExpression = createScheduleExpression(for: input.scheduledDate)

        let executionRoleArn = Environment.get("SMS_REMINDER_EXECUTION_ROLE_ARN") ?? ""

        print("🔧 Creating schedule with:")
        print("   - Lambda ARN: \(lambdaFunctionArn)")
        print("   - Execution Role ARN: \(executionRoleArn)")
        print("   - Schedule Group: \(scheduleGroupName)")
        print("   - Schedule Name: \(scheduleName)")
        print("   - Schedule Expression: \(scheduleExpression)")

        let target = Scheduler.Target(
            arn: lambdaFunctionArn,
            input: payloadString,
            roleArn: executionRoleArn
        )

        let createScheduleInput = Scheduler.CreateScheduleInput(
            description: "SMS reminder for \(input.entityType.rawValue) - \(input.memberName ?? "Unknown")",
            flexibleTimeWindow: Scheduler.FlexibleTimeWindow(mode: .off),
            groupName: scheduleGroupName,
            name: scheduleName,
            scheduleExpression: scheduleExpression,
            target: target
        )

        return scheduler.createSchedule(createScheduleInput).map { _ in
            // Log successful scheduling
            req.smsReminderLogger.logReminderScheduled(
                entityType: input.entityType,
                entityId: input.entityId,
                phoneNumber: input.phoneNumber,
                scheduledDate: input.scheduledDate,
                memberName: input.memberName ?? "Unknown"
            )
        }.flatMapError { error in
            let wrappedError = SMSReminderError.awsSchedulerError(error)
            req.smsReminderLogger.logSchedulingError(
                entityType: input.entityType,
                entityId: input.entityId,
                error: wrappedError,
                phoneNumber: input.phoneNumber
            )
            return req.eventLoop.makeFailedFuture(wrappedError)
        }
    }
    
    private func generateScheduleName(entityId: UUID, type: SMSReminderPayload.ReminderType) -> String {
        return "sms-reminder-\(type.rawValue)-\(entityId.uuidString)"
    }
    
    private func createScheduleExpression(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        formatter.timeZone = TimeZone(identifier: "UTC")
        return "at(\(formatter.string(from: date)))"
    }

    private func isValidPhoneNumber(_ phoneNumber: String) -> Bool {
        // Basic validation for international phone numbers
        // Should start with + and contain 10-15 digits
        let phoneRegex = try! NSRegularExpression(pattern: "^\\+[1-9]\\d{9,14}$")
        let range = NSRange(location: 0, length: phoneNumber.utf16.count)
        return phoneRegex.firstMatch(in: phoneNumber, options: [], range: range) != nil
    }

    // MARK: - Message Creation Methods

    private func createAppointmentReminderMessage(
        memberName: String,
        appointmentTitle: String,
        appointmentDate: Date,
        hoursUntil: Int
    ) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short

        let dateString = formatter.string(from: appointmentDate)
        let timeFrame = hoursUntil == 24 ? "tomorrow" : "in 2 days"

        return "Hi \(memberName)! Reminder: You have an appointment \(timeFrame) - \(appointmentTitle) on \(dateString). Please contact us if you need to reschedule."
    }

    private func createTaskReminderMessage(
        memberName: String,
        taskTitle: String,
        dueDate: Date
    ) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short

        let dateString = formatter.string(from: dueDate)

        return "Hi \(memberName)! Reminder: Your task '\(taskTitle)' is due tomorrow (\(dateString)). Please complete it when you have a chance."
    }

    private func createGoalReminderMessage(
        memberName: String,
        goalTitle: String,
        targetDate: Date
    ) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium

        let dateString = formatter.string(from: targetDate)

        return "Hi \(memberName)! Reminder: Your goal '\(goalTitle)' target date is tomorrow (\(dateString)). You're doing great - keep it up!"
    }

    private func createInterventionReminderMessage(
        memberName: String,
        interventionAction: String,
        dueDate: Date
    ) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium

        let dateString = formatter.string(from: dueDate)

        return "Hi \(memberName)! Reminder: Your intervention '\(interventionAction)' is due tomorrow (\(dateString)). Please follow up with your care team if needed."
    }

    private func createFollowUpReminderMessage(
        memberName: String,
        followUpType: String,
        followUpDate: Date
    ) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short

        let dateString = formatter.string(from: followUpDate)

        return "Hi \(memberName)! Reminder: You have a \(followUpType.lowercased()) follow-up appointment tomorrow (\(dateString)). Please contact us if you need to reschedule."
    }

    private func createProgramTaskReminderMessage(
        memberName: String,
        taskTitle: String,
        dueDate: Date
    ) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short

        let dateString = formatter.string(from: dueDate)

        return "Hi \(memberName)! Reminder: Your program task '\(taskTitle)' is due tomorrow (\(dateString)). Please complete it as part of your care program."
    }
}

// MARK: - Helper Extensions

extension SMSReminderPayload.ReminderType {
    var isAppointmentType: Bool {
        return self == .appointment24h || self == .appointment48h
    }
}

// MARK: - Application Extension

public extension Application {
    var smsReminderScheduler: SMSReminderScheduler {
        return SMSReminderScheduler(client: self.aws.client)
    }
}

public extension Request {
    var smsReminderScheduler: SMSReminderScheduler {
        return self.application.smsReminderScheduler
    }
}
